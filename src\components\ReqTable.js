import React, { useEffect, useRef, useState, useCallback } from "react";
import { History } from "history";
import { useHistory } from "react-router-dom";
import Button from "@material-ui/core/Button";
import Typography from "@material-ui/core/Typography";
import { useTheme } from "@material-ui/core/styles";
import Select from "@material-ui/core/Select";
import MenuItem from "@material-ui/core/MenuItem";
import FormControl from "@material-ui/core/FormControl";
import Box from "@material-ui/core/Box";
import IconButton from "@material-ui/core/IconButton";
import FirstPageIcon from "@material-ui/icons/FirstPage";
import LastPageIcon from "@material-ui/icons/LastPage";
import ChevronLeftIcon from "@material-ui/icons/ChevronLeft";
import ChevronRightIcon from "@material-ui/icons/ChevronRight";
import CircularProgress from "@material-ui/core/CircularProgress";
import { ColDef, GetRowIdParams, ICellRendererFunc } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import { OtpQsn, OtpQsnData } from "./TypeDefs";
import {
  defaultErrorHandler,
  fetchGetJson,
  getGridTheme,
  gridComponents,
  useStyles,
} from "./Util";

/** @type {ColDef<OtpQsn, any>} */
const defaultColDef = {
  resizable: true,
  floatingFilter: true,
  filter: true,
};

/** @type {AgGridReact<OtpQsn>} */
const emptyGridRef = undefined;

/** @type {Array<OtpQsn>} */
const emptyRowData = undefined;

/** @type {Array<OtpQsn>} */
const emptyDataList = [];

/**
 * @param {ColDef<OtpQsn, any>} colDef
 * @returns {ColDef<OtpQsn, any>}
 */
function createColDef(colDef) {
  return colDef;
}

/** @param {GetRowIdParams<OtpQsn>} params */
function getRowId(params) {
  return params.data.txId;
}

/** @type {ICellRendererFunc<OtpQsn>} */
const ActionCellRenderer = (props) => {
  /** @type {History} */
  const history = props.history;
  const data = props.data;

  const onClick = () => {
    history.push(`/req/${data.txId}`);
  };

  return (
    <Button variant="outlined" color="secondary" size="small" onClick={onClick}>
      View
    </Button>
  );
};

/** @type {ICellRendererFunc<OtpQsn>} */
const DateCellRenderer = (props) => {
  if(props.data.createdDate == null){
    return <></>;
  }

  return (new Date(props.data.createdDate)).toLocaleString('en-US');
}

const ReqTable = (props) => {
  const classes = useStyles();
  const theme = useTheme();
  const gridTheme = getGridTheme(theme);
  const history = useHistory();

  // State variables
  const [rowData, setRowData] = useState(emptyRowData);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(false);

  const gridRef = useRef(emptyGridRef);

  const [columnDefs, setColumnDefs] = useState([
    createColDef({
      headerName: "",
      width: 100,
      cellRenderer: ActionCellRenderer,
      cellRendererParams: { history },
      floatingFilter: false,
      filter:false,
    }),
    createColDef({
      headerName: "ID",
      field: "txId",
      width: 300,
    }),
    createColDef({
      headerName: "Request Date",
      field: "createdDate",
      width: 200,
      floatingFilter: false,
      cellRenderer: DateCellRenderer,
    }),
    createColDef({
      headerName: "Original OPN",
      field: "qsnData.order.opn",
      width: 200,
    }),
    createColDef({
      headerName: "New OPN",
      field: "opnNew",
      width: 200,
    }),
    createColDef({
      headerName: "New OPN GPN",
      field: "opnNewGpn",
      width: 200,
    }),
    createColDef({
      headerName: "Price",
      field: "price",
      width: 150,
    }),
    createColDef({
      headerName: "WW Account ID",
      field: "wwAccountId",
      width: 150,
    }),
    createColDef({
      headerName: "Company",
      field: "companyName",
      width: 150,
    }),
    createColDef({
      headerName: "Order Type",
      field: "qsnData.order.questionnaire.orderType",
      width: 150,
    }),
    createColDef({
      headerName: "Email",
      field: "qsnData.userInfo.email",
      width: 150,
    }),
    createColDef({
      headerName: "First Name",
      field: "qsnData.userInfo.firstname",
      width: 150,
    }),
    createColDef({
      headerName: "Last Name",
      field: "qsnData.userInfo.lastname",
      width: 150,
    }),
  ]);

  const formatDate = (data) => {
    return data.map((elem) => {
      if(elem.createdDate == null){
        return elem;
      }

      return {
        ...elem,
        // Remove the timestamp portion of Java Util Date
        createdDate: elem.createdDate.slice(0, elem.createdDate.length - 6),
      };
    })
  }

  // Function to fetch data with pagination
  const fetchData = useCallback(async (page = 0, size = pageSize) => {
    try {
      setLoading(true);
      const offset = page * size;
      const url = `/tpld-api/questionnaires/TPLD_CUSTOM_OPN?pageSize=${size}&offset=${offset}`;

      const response = await fetchGetJson(
        url,
        { data: emptyDataList, totalCount: 0 },
        "Cannot load OTP list"
      );

      // Check if response has the expected structure
      if (response && response.data && response.totalCount !== undefined) {
        // Backend returns { data: [...], totalCount: number }
        setRowData(formatDate(response.data));
        setTotalCount(response.totalCount);
      } else {
        // Handle case where response is just an array (for backward compatibility)
        setRowData(formatDate(response));
        setTotalCount(response.length);
      }

      setCurrentPage(page);
    } catch (error) {
      defaultErrorHandler(error);
    } finally {
      setLoading(false);
    }
  }, [pageSize]);

  useEffect(() => {
    fetchData(0, pageSize);
  }, [fetchData, pageSize]);

  // Handle page size change
  const handlePageSizeChange = (event) => {
    const newPageSize = parseInt(event.target.value);
    setPageSize(newPageSize);
    fetchData(0, newPageSize);
  };

  // Navigation functions
  const goToFirstPage = () => fetchData(0, pageSize);
  const goToPreviousPage = () => fetchData(Math.max(0, currentPage - 1), pageSize);
  const goToNextPage = () => fetchData(Math.min(Math.ceil(totalCount / pageSize) - 1, currentPage + 1), pageSize);
  const goToLastPage = () => fetchData(Math.ceil(totalCount / pageSize) - 1, pageSize);

  // Calculate pagination info
  const startRow = currentPage * pageSize + 1;
  const endRow = Math.min((currentPage + 1) * pageSize, totalCount);
  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className={classes.fullHeight} style={{ position: 'relative' }}>
      <Typography variant="h4">TPLD Requests</Typography>
      <div className={`${gridTheme} ${classes.tiAgGrid}`}>
        <AgGridReact
          ref={gridRef}
          components={gridComponents}
          rowData={rowData}
          rowHeight={40}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          getRowId={getRowId}
          enableCellTextSelection={true}
          stopEditingWhenCellsLoseFocus={true}
          suppressDragLeaveHidesColumns={true}
        ></AgGridReact>
      </div>

      {/* Pagination Controls */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mt={1} mb={1} style={{ height: '20px' }}>
        <Box display="flex" alignItems="center">
          <Typography variant="body2" style={{ marginRight: 16, fontSize: '0.875rem' }}>
            Showing {rowData && rowData.length > 0 ? startRow : 0} to {rowData && rowData.length > 0 ? endRow : 0} of {totalCount} entries
          </Typography>

          {/* Page Size Selector */}
          <Box display="flex" alignItems="center">
            <Typography variant="body2" style={{ marginRight: 8, fontSize: '0.875rem' }}>Rows per page:</Typography>
            <FormControl variant="outlined" size="small" style={{ minWidth: 70 }}>
              <Select
                value={pageSize}
                onChange={handlePageSizeChange}
                displayEmpty
                variant="outlined"
                size="small"
                style={{ height: '32px' }}
              >
                <MenuItem value={5}>5</MenuItem>
                <MenuItem value={10}>10</MenuItem>
                <MenuItem value={25}>25</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>

        <Box display="flex" alignItems="center">
          <IconButton
            size="small"
            onClick={goToFirstPage}
            disabled={currentPage === 0}
            style={{ padding: 4 }}
          >
            <FirstPageIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={goToPreviousPage}
            disabled={currentPage === 0}
            style={{ padding: 4 }}
          >
            <ChevronLeftIcon fontSize="small" />
          </IconButton>
          <Typography variant="body2" component="span" style={{ margin: '0 8px', fontSize: '0.875rem' }}>
            {currentPage + 1} of {totalPages || 1}
          </Typography>
          <IconButton
            size="small"
            onClick={goToNextPage}
            disabled={currentPage >= totalPages - 1}
            style={{ padding: 4 }}
          >
            <ChevronRightIcon fontSize="small" />
          </IconButton>
          <IconButton
            size="small"
            onClick={goToLastPage}
            disabled={currentPage >= totalPages - 1}
            style={{ padding: 4 }}
          >
            <LastPageIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      {/* Loading Overlay */}
      {loading && (
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          display="flex"
          justifyContent="center"
          alignItems="center"
          zIndex={1000}
          bgcolor="rgba(255, 255, 255, 0.7)"
        >
          <CircularProgress size={60} />
        </Box>
      )}
    </div>
  );
};

export default ReqTable;
